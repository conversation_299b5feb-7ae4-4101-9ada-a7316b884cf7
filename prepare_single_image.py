#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to prepare a single image for inference with the FilmRemoval model.
The model expects 4 polarized images (0°, 135°, 45°, 90°) but we only have one image.
This script duplicates the single image to create the expected structure.
"""

import os
import shutil
import cv2

def prepare_single_image(input_image_path, output_dir):
    """
    Prepare a single image for inference by creating the expected directory structure.
    
    Args:
        input_image_path: Path to the input image
        output_dir: Output directory to create the structure
    """
    
    # Create directory structure
    k1_input_dir = os.path.join(output_dir, "K1", "input")
    k1_gt_dir = os.path.join(output_dir, "K1", "GT")
    
    os.makedirs(k1_input_dir, exist_ok=True)
    os.makedirs(k1_gt_dir, exist_ok=True)
    
    # Get the base name without extension
    base_name = os.path.splitext(os.path.basename(input_image_path))[0]
    
    # The model expects 4 images per sample in a specific naming pattern
    # We'll create 4 copies of the same image to simulate polarized inputs
    polarization_angles = ["0", "135", "45", "90"]
    
    for i, angle in enumerate(polarization_angles):
        # Create filename for each polarization angle
        output_filename = f"{base_name}_{angle}.jpg"
        output_path = os.path.join(k1_input_dir, output_filename)
        
        # Copy the input image
        shutil.copy2(input_image_path, output_path)
        print(f"Created: {output_path}")
    
    # Also create a ground truth copy (for reference, though it won't be used in inference)
    gt_filename = f"{base_name}.jpg"
    gt_path = os.path.join(k1_gt_dir, gt_filename)
    shutil.copy2(input_image_path, gt_path)
    print(f"Created GT: {gt_path}")
    
    print(f"Successfully prepared image structure in: {output_dir}")

if __name__ == "__main__":
    input_image = "test_images/test_06.jpg"
    output_directory = "test_data_prepared"
    
    if not os.path.exists(input_image):
        print(f"Error: Input image {input_image} not found!")
        exit(1)
    
    prepare_single_image(input_image, output_directory)
