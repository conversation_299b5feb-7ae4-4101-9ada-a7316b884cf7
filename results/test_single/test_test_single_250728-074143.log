25-07-28 07:41:43.444 - INFO:   name: test_single
  model: condition
  distortion: sr
  scale: 1
  gpu_ids: []
  datasets:[
    test:[
      name: Reconstruction
      mode: LQGT_condition_Val
      Test_K_ford: K1
      dataroot: ../test_data_prepared
      condition: image
      phase: test
      scale: 1
      data_type: img
    ]
  ]
  path:[
    root: /qsfs2/qsuser/harsh.baudh/FilmRemoval/./
    pretrain_model_G: ../FilmRemoval_Pretrained/K1/300000_G.pth
    results_root: /qsfs2/qsuser/harsh.baudh/FilmRemoval/./results/test_single
    log: /qsfs2/qsuser/harsh.baudh/FilmRemoval/./results/test_single
  ]
  is_train: False

25-07-28 07:41:43.547 - INFO: Dataset [LQGT_dataset_Val - Reconstruction] is created.
25-07-28 07:41:43.547 - INFO: Number of test images in [Reconstruction]: 1
25-07-28 07:41:43.935 - INFO: Loading model for G [../FilmRemoval_Pretrained/K1/300000_G.pth] ...
25-07-28 07:41:43.949 - INFO: Model [GenerationModel] is created.
